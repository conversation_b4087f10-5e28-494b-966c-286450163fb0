package com.nacos.controller.media;

import com.nacos.entity.dto.VideoTranslateRequestDTO;
import com.nacos.entity.vo.VideoTranslateStatusVO;
import com.nacos.entity.vo.LanguageVO;
import com.nacos.entity.vo.VoiceVO;
import com.nacos.result.Result;
import com.nacos.service.VideoTranslateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 视频翻译Controller（基于羚羊平台）
 * 提供视频翻译的RESTful API接口，包括任务提交、状态查询、语种音色获取、任务管理等功能
 * 重构后使用VideoTranslateService进行业务处理
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
@Slf4j
@Tag(name = "视频翻译", description = "基于羚羊平台的视频翻译功能")
@RestController
@RequestMapping("/api/video-translate")
@Validated
@RequiredArgsConstructor
public class VideoTranslateController {

    private final VideoTranslateService videoTranslateService;

    /**
     * 提交视频翻译任务
     * URL: /api/video-translate/submit
     * 
     * @param request 翻译请求参数
     * @return 任务提交结果
     */
    @Operation(summary = "提交视频翻译任务", description = "提交视频文件进行语言翻译处理")
    @PostMapping("/submit")
    public Result<Map<String, Object>> submitTranslateTask(@Valid @RequestBody VideoTranslateRequestDTO request) {
        String methodName = "submitTranslateTask";
        try {
            log.info("[{}] 提交视频翻译任务: userId={}, {}", methodName, request.getUserId(), request.getLanguagePairDescription());

            // 调用服务层提交任务
            Result<Map<String, Object>> result = videoTranslateService.submitTranslateTask(request);

            if (result.isSuccess()) {
                log.info("[{}] 视频翻译任务提交成功: userId={}, taskId={}",
                    methodName, request.getUserId(), result.getData().get("taskId"));
            } else {
                log.error("[{}] 视频翻译任务提交失败: userId={}, error={}",
                    methodName, request.getUserId(), result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 提交视频翻译任务异常: userId={}, error={}", methodName, request.getUserId(), e.getMessage(), e);
            return Result.ERROR("提交任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    @Operation(summary = "查询任务状态", description = "根据任务ID查询视频翻译任务的当前状态和进度")
    @GetMapping("/status/{taskId}")
    public Result<VideoTranslateStatusVO> getTaskStatus(
            @Parameter(description = "任务ID") @PathVariable @NotBlank String taskId) {
        String methodName = "getTaskStatus";
        try {
            log.info("[{}] 查询任务状态: taskId={}", methodName, taskId);

            // 调用服务层查询状态
            Result<VideoTranslateStatusVO> result = videoTranslateService.getTaskStatus(taskId);

            if (result.isSuccess()) {
                log.info("[{}] 查询任务状态成功: taskId={}, status={}", methodName, taskId, result.getData().getStatus());
            } else {
                log.error("[{}] 查询任务状态失败: taskId={}, error={}", methodName, taskId, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 查询任务状态异常: taskId={}, error={}", methodName, taskId, e.getMessage(), e);
            return Result.ERROR("查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的语种列表
     * 
     * @return 语种列表
     */
    @Operation(summary = "获取支持的语种列表", description = "获取羚羊平台支持的所有语种信息")
    @GetMapping("/languages")
    public Result<List<LanguageVO>> getSupportedLanguages() {
        String methodName = "getSupportedLanguages";
        try {
            log.info("[{}] 获取支持的语种列表", methodName);

            // 调用服务层获取语种列表
            Result<List<LanguageVO>> result = videoTranslateService.getSupportedLanguages();

            if (result.isSuccess()) {
                log.info("[{}] 获取语种列表成功: count={}", methodName, result.getData().size());
            } else {
                log.error("[{}] 获取语种列表失败: error={}", methodName, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 获取语种列表异常: {}", methodName, e.getMessage(), e);
            return Result.ERROR("获取语种列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定语种的音色列表
     * 
     * @param languageCode 语种代码
     * @param gender 性别筛选（可选）
     * @return 音色列表
     */
    @Operation(summary = "获取音色列表", description = "根据语种代码获取对应的音色列表，支持性别筛选")
    @GetMapping("/voices")
    public Result<List<VoiceVO>> getSupportedVoices(
            @Parameter(description = "语种代码") @RequestParam @NotBlank String languageCode,
            @Parameter(description = "性别筛选：1-女 2-男") @RequestParam(required = false) Integer gender) {
        String methodName = "getSupportedVoices";
        try {
            log.info("[{}] 获取音色列表: languageCode={}, gender={}", methodName, languageCode, gender);

            // 调用服务层获取音色列表
            Result<List<VoiceVO>> result = videoTranslateService.getSupportedVoices(languageCode, gender);

            if (result.isSuccess()) {
                log.info("[{}] 获取音色列表成功: languageCode={}, count={}", methodName, languageCode, result.getData().size());
            } else {
                log.error("[{}] 获取音色列表失败: languageCode={}, error={}", methodName, languageCode, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 获取音色列表异常: languageCode={}, error={}", methodName, languageCode, e.getMessage(), e);
            return Result.ERROR("获取音色列表失败: " + e.getMessage());
        }
    }

    /**
     * 取消翻译任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 取消结果
     */
    @Operation(summary = "取消翻译任务", description = "取消正在处理中的视频翻译任务")
    @PostMapping("/cancel/{taskId}")
    public Result<String> cancelTask(
            @Parameter(description = "任务ID") @PathVariable @NotBlank String taskId,
            @Parameter(description = "用户ID") @RequestParam @NotBlank String userId) {
        String methodName = "cancelTask";
        try {
            log.info("[{}] 取消翻译任务: taskId={}, userId={}", methodName, taskId, userId);

            // 调用服务层取消任务
            Result<String> result = videoTranslateService.cancelTask(taskId, userId);

            if (result.isSuccess()) {
                log.info("[{}] 翻译任务取消成功: taskId={}", methodName, taskId);
            } else {
                log.error("[{}] 翻译任务取消失败: taskId={}, error={}", methodName, taskId, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 取消翻译任务异常: taskId={}, error={}", methodName, taskId, e.getMessage(), e);
            return Result.ERROR("取消任务失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询任务历史
     */
    @Operation(summary = "分页查询任务历史", description = "支持多条件查询和排序的任务历史分页接口")
    @GetMapping("/history")
    public Result<Map<String, Object>> queryTaskHistory(
            @Parameter(description = "用户ID（可选）") @RequestParam(required = false) String userId,
            @Parameter(description = "任务状态（可选）") @RequestParam(required = false) Integer status,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "页码，从1开始") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdTime") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "DESC") String sortOrder,
            HttpServletRequest request) {
        String methodName = "queryTaskHistory";
        try {
            log.info("[{}] 查询任务历史: userId={}, status={}, page={}, size={}",
                methodName, userId, status, page, size);

            // 如果没有指定userId，可以从请求中获取当前用户ID
            // if (userId == null) {
            //     userId = getCurrentUserId(request);
            // }

            Result<Map<String, Object>> result = videoTranslateService.queryTaskHistory(
                userId, status, startTime, endTime, page, size, sortBy, sortOrder);

            if (result.isSuccess()) {
                log.info("[{}] 查询任务历史成功: total={}", methodName, result.getData().get("total"));
            } else {
                log.error("[{}] 查询任务历史失败: error={}", methodName, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 查询任务历史异常: error={}", methodName, e.getMessage(), e);
            return Result.ERROR("查询任务历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户任务历史统计
     */
    @Operation(summary = "获取用户任务历史统计", description = "获取指定用户在指定天数内的任务统计信息")
    @GetMapping("/history/stats")
    public Result<Map<String, Object>> getUserTaskHistoryStats(
            @Parameter(description = "用户ID") @RequestParam @NotBlank String userId,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") Integer days) {
        String methodName = "getUserTaskHistoryStats";
        try {
            log.info("[{}] 获取用户任务历史统计: userId={}, days={}", methodName, userId, days);

            Result<Map<String, Object>> result = videoTranslateService.getUserTaskHistoryStats(userId, days);

            if (result.isSuccess()) {
                log.info("[{}] 获取用户任务历史统计成功: userId={}", methodName, userId);
            } else {
                log.error("[{}] 获取用户任务历史统计失败: userId={}, error={}", methodName, userId, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 获取用户任务历史统计异常: userId={}, error={}", methodName, userId, e.getMessage(), e);
            return Result.ERROR("获取历史统计失败: " + e.getMessage());
        }
    }

    /**
     * 导出任务历史数据
     */
    @Operation(summary = "导出任务历史数据", description = "导出任务历史数据为CSV或Excel格式")
    @GetMapping("/history/export")
    public Result<Map<String, Object>> exportTaskHistory(
            @Parameter(description = "用户ID（可选）") @RequestParam(required = false) String userId,
            @Parameter(description = "任务状态（可选）") @RequestParam(required = false) Integer status,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "导出格式") @RequestParam(defaultValue = "CSV") String format) {
        String methodName = "exportTaskHistory";
        try {
            log.info("[{}] 导出任务历史: userId={}, status={}, format={}", methodName, userId, status, format);

            Result<Map<String, Object>> result = videoTranslateService.exportTaskHistory(
                userId, status, startTime, endTime, format);

            if (result.isSuccess()) {
                log.info("[{}] 导出任务历史成功: recordCount={}", methodName, result.getData().get("recordCount"));
            } else {
                log.error("[{}] 导出任务历史失败: error={}", methodName, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 导出任务历史异常: error={}", methodName, e.getMessage(), e);
            return Result.ERROR("导出任务历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务执行趋势数据
     */
    @Operation(summary = "获取任务执行趋势", description = "获取指定时间范围内的任务执行趋势数据")
    @GetMapping("/history/trend")
    public Result<Map<String, Object>> getTaskTrendData(
            @Parameter(description = "用户ID（可选）") @RequestParam(required = false) String userId,
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "7") Integer days,
            @Parameter(description = "时间粒度") @RequestParam(defaultValue = "DAY") String granularity) {
        String methodName = "getTaskTrendData";
        try {
            log.info("[{}] 获取任务执行趋势: userId={}, days={}, granularity={}",
                methodName, userId, days, granularity);

            Result<Map<String, Object>> result = videoTranslateService.getTaskTrendData(userId, days, granularity);

            if (result.isSuccess()) {
                log.info("[{}] 获取任务执行趋势成功: userId={}", methodName, userId);
            } else {
                log.error("[{}] 获取任务执行趋势失败: error={}", methodName, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 获取任务执行趋势异常: error={}", methodName, e.getMessage(), e);
            return Result.ERROR("获取任务趋势失败: " + e.getMessage());
        }
    }

    /**
     * 系统健康检查
     *
     * @return 系统状态
     */
    @Operation(summary = "系统健康检查", description = "检查视频翻译系统的健康状态和配置信息")
    @GetMapping("/health")
    public Result<Map<String, Object>> healthCheck() {
        String methodName = "healthCheck";
        try {
            log.info("[{}] 系统健康检查", methodName);

            // 调用服务层进行健康检查
            Result<Map<String, Object>> result = videoTranslateService.healthCheck();

            if (result.isSuccess()) {
                log.info("[{}] 系统健康检查通过", methodName);
            } else {
                log.warn("[{}] 系统健康检查发现问题: {}", methodName, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 系统健康检查异常: {}", methodName, e.getMessage(), e);
            return Result.ERROR("健康检查失败: " + e.getMessage());
        }
    }

    // ==================== 新增接口方法 ====================

    /**
     * 获取用户任务统计信息
     */
    @Operation(summary = "获取用户任务统计", description = "获取用户的任务统计信息，包括总数、完成数、处理中等")
    @GetMapping("/stats")
    public Result<Map<String, Object>> getUserTaskStats(
            @Parameter(description = "用户ID") @RequestParam @NotBlank String userId) {
        String methodName = "getUserTaskStats";
        try {
            log.info("[{}] 获取用户任务统计: userId={}", methodName, userId);

            // 调用服务层获取统计信息
            Result<Map<String, Object>> result = videoTranslateService.getUserTaskStats(userId);

            if (result.isSuccess()) {
                log.info("[{}] 获取用户任务统计成功: userId={}", methodName, userId);
            } else {
                log.error("[{}] 获取用户任务统计失败: userId={}, error={}", methodName, userId, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 获取用户任务统计异常: userId={}, error={}", methodName, userId, e.getMessage(), e);
            return Result.ERROR("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 重试失败的任务
     */
    @Operation(summary = "重试任务", description = "重新提交失败或超时的翻译任务")
    @PostMapping("/retry/{taskId}")
    public Result<String> retryTask(
            @Parameter(description = "任务ID") @PathVariable @NotBlank String taskId,
            @Parameter(description = "用户ID") @RequestParam @NotBlank String userId) {
        String methodName = "retryTask";
        try {
            log.info("[{}] 重试翻译任务: taskId={}, userId={}", methodName, taskId, userId);

            // 调用服务层重试任务
            Result<String> result = videoTranslateService.retryTask(taskId, userId);

            if (result.isSuccess()) {
                log.info("[{}] 翻译任务重试成功: taskId={}", methodName, taskId);
            } else {
                log.error("[{}] 翻译任务重试失败: taskId={}, error={}", methodName, taskId, result.getMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("[{}] 重试翻译任务异常: taskId={}, error={}", methodName, taskId, e.getMessage(), e);
            return Result.ERROR("重试任务失败: " + e.getMessage());
        }
    }
}
