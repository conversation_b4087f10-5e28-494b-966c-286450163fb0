package com.nacos.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 视频翻译任务实体类（基于羚羊平台）
 * 用于存储羚羊平台视频翻译任务的状态信息，包括任务ID、用户ID、源视频、目标语言、羚羊任务ID、进度状态等
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@TableName("digital_video_translation_task")
@Schema(description = "视频翻译任务实体类")
public class VideoTranslateTaskPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务唯一标识
     */
    @Schema(description = "任务唯一标识")
    @TableField("task_id")
    private String taskId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @TableField("user_id")
    private String userId;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    @TableField("task_name")
    private String taskName;

    /**
     * 源视频URL
     */
    @Schema(description = "源视频URL")
    @TableField("source_video_url")
    private String sourceVideoUrl;

    /**
     * 源语言代码
     */
    @Schema(description = "源语言代码", example = "cn")
    @TableField("source_language")
    private String sourceLanguage;

    /**
     * 源语言显示名称
     */
    @Schema(description = "源语言显示名称", example = "中文")
    @TableField("source_language_name")
    private String sourceLanguageName;

    /**
     * 目标语言代码
     */
    @Schema(description = "目标语言代码", example = "en")
    @TableField("target_language")
    private String targetLanguage;

    /**
     * 目标语言显示名称
     */
    @Schema(description = "目标语言显示名称", example = "英文")
    @TableField("target_language_name")
    private String targetLanguageName;

    /**
     * 羚羊平台任务ID
     */
    @Schema(description = "羚羊平台任务ID")
    @TableField("lingyang_task_id")
    private String lingyangTaskId;

    /**
     * 音色ID
     */
    @Schema(description = "音色ID")
    @TableField("voice_id")
    private String voiceId;

    /**
     * 音色名称
     */
    @Schema(description = "音色名称")
    @TableField("voice_name")
    private String voiceName;

    /**
     * 任务状态（羚羊平台状态）：submitted-已提交 processing-处理中 completed-已完成 failed-失败 cancelled-已取消 timeout-超时
     */
    @Schema(description = "任务状态", example = "submitted")
    @TableField("status")
    private String status;

    /**
     * 任务进度百分比(0-100)
     */
    @Schema(description = "任务进度百分比(0-100)")
    @TableField("progress")
    private Integer progress;

    /**
     * 当前处理步骤描述
     */
    @Schema(description = "当前处理步骤描述")
    @TableField("current_step")
    private String currentStep;

    /**
     * 结果视频URL
     */
    @Schema(description = "结果视频URL")
    @TableField("result_video_url")
    private String resultVideoUrl;

    /**
     * 字幕文件URL
     */
    @Schema(description = "字幕文件URL")
    @TableField("subtitle_url")
    private String subtitleUrl;

    /**
     * 封面图片URL
     */
    @Schema(description = "封面图片URL")
    @TableField("cover_url")
    private String coverUrl;

    /**
     * 识别的原文本
     */
    @Schema(description = "识别的原文本")
    @TableField("original_text")
    private String originalText;

    /**
     * 翻译后的文本
     */
    @Schema(description = "翻译后的文本")
    @TableField("translated_text")
    private String translatedText;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    @TableField("error_message")
    private String errorMessage;

    /**
     * 源视频时长（秒）
     */
    @Schema(description = "源视频时长（秒）")
    @TableField("video_duration")
    private Double videoDuration;

    /**
     * 源视频文件大小（字节）
     */
    @Schema(description = "源视频文件大小（字节）")
    @TableField("video_file_size")
    private Long videoFileSize;

    /**
     * 视频格式
     */
    @Schema(description = "视频格式", example = "mp4")
    @TableField("video_format")
    private String videoFormat;

    /**
     * 预计完成时间（分钟）
     */
    @Schema(description = "预计完成时间（分钟）")
    @TableField("estimated_minutes")
    private Integer estimatedMinutes;

    /**
     * 实际处理时长（秒）
     */
    @Schema(description = "实际处理时长（秒）")
    @TableField("processing_duration")
    private Long processingDuration;

    /**
     * 费用金额
     */
    @Schema(description = "费用金额")
    @TableField("cost")
    private Double cost;

    /**
     * 任务配置参数（JSON格式）
     */
    @Schema(description = "任务配置参数（JSON格式）")
    @TableField("config_params")
    private String configParams;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 开始处理时间
     */
    @Schema(description = "开始处理时间")
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    @TableField("finish_time")
    private LocalDateTime finishTime;

    /**
     * 是否删除：0-未删除 1-已删除
     */
    @Schema(description = "是否删除：0-未删除 1-已删除")
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;

    // ==================== 业务方法 ====================

    /**
     * 检查任务是否处理中
     */
    public boolean isProcessing() {
        return status != null && (
            status.equals("submitted") ||
            status.equals("processing") ||
            status.equals("waiting") ||
            status.equals("preparing") ||
            status.equals("uploading")
        );
    }

    /**
     * 检查任务是否已完成
     */
    public boolean isCompleted() {
        return "completed".equals(status);
    }

    /**
     * 检查任务是否失败
     */
    public boolean isFailed() {
        return status != null && (
            status.equals("failed") ||
            status.equals("timeout")
        );
    }

    /**
     * 检查任务是否已取消
     */
    public boolean isCancelled() {
        return "cancelled".equals(status);
    }

    /**
     * 检查任务是否为最终状态
     */
    public boolean isFinalStatus() {
        return isCompleted() || isFailed() || isCancelled();
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知状态";
        }

        switch (status) {
            case "submitted": return "已提交";
            case "waiting": return "等待中";
            case "preparing": return "准备中";
            case "uploading": return "上传中";
            case "processing": return "处理中";
            case "completed": return "已完成";
            case "failed": return "失败";
            case "timeout": return "超时";
            case "cancelled": return "已取消";
            default: return "未知状态";
        }
    }

    /**
     * 获取语言对描述
     */
    public String getLanguagePairDescription() {
        if (sourceLanguageName != null && targetLanguageName != null) {
            return sourceLanguageName + " → " + targetLanguageName;
        } else if (sourceLanguage != null && targetLanguage != null) {
            return sourceLanguage + " → " + targetLanguage;
        }
        return "未知语言对";
    }

    /**
     * 计算处理进度百分比
     */
    public int calculateProgress() {
        if (progress != null) {
            return progress;
        }

        if (status == null) {
            return 0;
        }

        // 根据羚羊平台状态估算进度
        switch (status) {
            case "submitted": return 5;   // 已提交
            case "waiting": return 10;    // 等待中
            case "preparing": return 20;  // 准备中
            case "uploading": return 15;  // 上传中
            case "processing": return 50; // 处理中
            case "completed": return 100; // 已完成
            case "failed":
            case "timeout":
            case "cancelled": return -1;  // 失败状态
            default: return 0;
        }
    }

    /**
     * 检查是否有结果文件
     */
    public boolean hasResultFiles() {
        return (resultVideoUrl != null && !resultVideoUrl.trim().isEmpty()) ||
               (subtitleUrl != null && !subtitleUrl.trim().isEmpty());
    }

    /**
     * 检查是否有视频结果
     */
    public boolean hasVideoResult() {
        return resultVideoUrl != null && !resultVideoUrl.trim().isEmpty();
    }

    /**
     * 检查是否有字幕结果
     */
    public boolean hasSubtitleResult() {
        return subtitleUrl != null && !subtitleUrl.trim().isEmpty();
    }

    /**
     * 获取音色描述
     */
    public String getVoiceDescription() {
        if (voiceName != null && !voiceName.trim().isEmpty()) {
            return voiceName + " (" + voiceId + ")";
        } else if (voiceId != null && !voiceId.trim().isEmpty()) {
            return "音色ID: " + voiceId;
        }
        return "默认音色";
    }

    /**
     * 获取任务摘要信息
     */
    public String getTaskSummary() {
        return String.format("任务[%s]: %s %s, 状态: %s (%d%%)",
            taskId,
            taskName != null ? taskName : "视频翻译",
            getLanguagePairDescription(),
            getStatusDescription(),
            calculateProgress());
    }

    /**
     * 获取处理时长描述
     */
    public String getProcessingDurationDescription() {
        if (processingDuration == null || processingDuration <= 0) {
            return "未知";
        }

        long minutes = processingDuration / 60;
        long seconds = processingDuration % 60;

        if (minutes > 0) {
            return minutes + "分" + seconds + "秒";
        } else {
            return seconds + "秒";
        }
    }
}
