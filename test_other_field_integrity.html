<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Other字段数据完整性测试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        textarea { width: 100%; height: 200px; margin: 10px 0; font-family: monospace; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .json-display { background: #f8f9fa; padding: 10px; border-radius: 3px; white-space: pre-wrap; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Other字段数据完整性测试工具</h1>
        
        <div class="test-section">
            <h2>配置</h2>
            <label>API Base URL:</label>
            <input type="text" id="baseUrl" value="http://localhost:8818" style="width: 300px;">
            <label>用户ID:</label>
            <input type="text" id="userId" value="test_user_123" style="width: 200px;">
        </div>

        <div class="test-section">
            <h2>测试数据</h2>
            <label>测试用例:</label>
            <select id="testCase" onchange="loadTestCase()">
                <option value="object">对象格式（前端常见）</option>
                <option value="string">字符串格式（推荐）</option>
                <option value="special">特殊字符测试</option>
                <option value="custom">自定义</option>
            </select>
            <br><br>
            <label>Other字段数据:</label>
            <textarea id="otherData" placeholder="输入other字段的测试数据"></textarea>
        </div>

        <div class="test-section">
            <h2>测试操作</h2>
            <button onclick="runTest()">运行完整测试</button>
            <button onclick="submitTask()">仅提交任务</button>
            <button onclick="queryTask()">仅查询任务</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div class="test-section">
            <h2>测试结果</h2>
            <div id="results"></div>
        </div>
    </div>

    <script>
        let currentTaskId = null;

        // 预定义测试用例
        const testCases = {
            object: {
                bgUrl: null,
                videoOffset: "Offset(0.0, 0.0)",
                videoScale: 1.0,
                textOffset: "Offset(0.0, 0.0)",
                textScale: 1.0,
                voiceId: "voice_19231863_5048250001",
                provider: "MINIMAX",
                duration: 1.296,
                text: "不不不",
                subtitles: [{
                    text: "不不不",
                    timeBegin: 0.0,
                    timeEnd: 1130.657596371882,
                    textBegin: 0,
                    textEnd: 3,
                    timestampedWords: null
                }]
            },
            string: '{"videoOffset":"Offset(0.0, 0.0)","videoScale":1.0,"duration":1.296,"provider":"MINIMAX"}',
            special: {
                specialChars: '包含"引号"和\n换行符\t制表符',
                emptyString: "",
                nullValue: null,
                spaces: "  前后有空格  ",
                offset1: "Offset(10.5, -5.2)",
                offset2: "Offset(0.0, 0.0)",
                numbers: [1.296, 0, -10.5],
                boolean: true
            }
        };

        function loadTestCase() {
            const caseType = document.getElementById('testCase').value;
            if (caseType !== 'custom') {
                const data = testCases[caseType];
                document.getElementById('otherData').value = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
            }
        }

        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<h3>${title}</h3><div class="json-display">${content}</div>`;
            results.appendChild(div);
        }

        async function submitTask() {
            const baseUrl = document.getElementById('baseUrl').value;
            const userId = document.getElementById('userId').value;
            const otherDataText = document.getElementById('otherData').value;
            
            let otherData;
            try {
                // 尝试解析为JSON对象
                otherData = JSON.parse(otherDataText);
            } catch (e) {
                // 如果解析失败，当作字符串处理
                otherData = otherDataText;
            }

            const requestData = {
                userId: userId,
                taskItems: [{
                    avatarId: "1928331425668972545",
                    voiceUrl: "https://example.com/test.mp3",
                    other: otherData
                }]
            };

            addResult('发送的数据', JSON.stringify(requestData, null, 2), 'info');

            try {
                const response = await fetch(`${baseUrl}/digital/video/edit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();
                
                if (result.success) {
                    currentTaskId = result.data;
                    addResult('提交成功', `任务ID: ${currentTaskId}`, 'success');
                    return currentTaskId;
                } else {
                    addResult('提交失败', JSON.stringify(result, null, 2), 'error');
                    return null;
                }
            } catch (error) {
                addResult('提交异常', error.message, 'error');
                return null;
            }
        }

        async function queryTask() {
            if (!currentTaskId) {
                addResult('查询失败', '请先提交任务获取taskId', 'warning');
                return null;
            }

            const baseUrl = document.getElementById('baseUrl').value;

            try {
                const response = await fetch(`${baseUrl}/digital/video/edit/${currentTaskId}`);
                const result = await response.json();

                if (result.success && result.data.items && result.data.items.length > 0) {
                    const receivedOther = result.data.items[0].other;
                    addResult('查询成功', `接收到的other字段:\n${receivedOther}`, 'success');
                    return receivedOther;
                } else {
                    addResult('查询失败', JSON.stringify(result, null, 2), 'error');
                    return null;
                }
            } catch (error) {
                addResult('查询异常', error.message, 'error');
                return null;
            }
        }

        async function runTest() {
            clearResults();
            addResult('开始测试', '正在执行完整的数据完整性测试...', 'info');

            // 1. 提交任务
            const taskId = await submitTask();
            if (!taskId) return;

            // 2. 等待一下让任务创建完成
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 3. 查询任务
            const receivedOther = await queryTask();
            if (!receivedOther) return;

            // 4. 数据完整性验证
            const originalData = document.getElementById('otherData').value;
            let originalOther;
            
            try {
                // 尝试解析原始数据
                originalOther = JSON.parse(originalData);
                if (typeof originalOther === 'object') {
                    originalOther = JSON.stringify(originalOther);
                }
            } catch (e) {
                originalOther = originalData;
            }

            // 比较数据
            try {
                const receivedParsed = JSON.parse(receivedOther);
                const originalParsed = JSON.parse(originalOther);
                
                const isIdentical = JSON.stringify(receivedParsed) === JSON.stringify(originalParsed);
                
                if (isIdentical) {
                    addResult('数据完整性验证', '✅ 数据完全一致！other字段保持原样存储', 'success');
                } else {
                    addResult('数据完整性验证', `❌ 数据不一致！\n原始: ${JSON.stringify(originalParsed, null, 2)}\n接收: ${JSON.stringify(receivedParsed, null, 2)}`, 'error');
                }

                // 特殊格式验证
                if (receivedParsed.videoOffset && receivedParsed.videoOffset.includes('Offset(')) {
                    addResult('特殊格式验证', '✅ Offset格式保持完整', 'success');
                } else if (originalParsed.videoOffset && originalParsed.videoOffset.includes('Offset(')) {
                    addResult('特殊格式验证', '❌ Offset格式发生变化', 'error');
                }

            } catch (e) {
                addResult('数据完整性验证', `解析失败: ${e.message}`, 'error');
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 页面加载时初始化
        window.onload = function() {
            loadTestCase();
        };
    </script>
</body>
</html>
